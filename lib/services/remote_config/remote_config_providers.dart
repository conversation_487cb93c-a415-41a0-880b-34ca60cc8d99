import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voji/services/remote_config/remote_config_service.dart';
import 'package:voji/utils/logger.dart';

/// Provider for the Remote Config service
final remoteConfigServiceProvider = Provider<RemoteConfigService>((ref) {
  return RemoteConfigService.instance;
});

/// Provider that tracks when remote config is updated
/// This can be used to trigger rebuilds when config changes
final remoteConfigUpdateProvider = StreamProvider<void>((ref) {
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return remoteConfigService.onConfigUpdated;
});

/// Provider for LLM prompts that automatically updates when remote config changes
final llmPromptsProvider = Provider<LlmPrompts>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);
  
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return LlmPrompts(remoteConfigService);
});

/// Provider for LLM model configurations that automatically updates when remote config changes
final llmModelConfigProvider = Provider<LlmModelConfig>((ref) {
  // Watch for remote config updates to trigger rebuilds
  ref.watch(remoteConfigUpdateProvider);
  
  final remoteConfigService = ref.watch(remoteConfigServiceProvider);
  return LlmModelConfig(remoteConfigService);
});

/// Class that provides access to all LLM prompts from Remote Config
class LlmPrompts {
  final RemoteConfigService _remoteConfigService;
  
  LlmPrompts(this._remoteConfigService);

  /// Get the prompt for transcribing audio to create a new ideabook
  String getNewIdeabookPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'audio_transcription_new_ideabook_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for transcribing audio to create a new idea
  /// Variables: {{ideabook_name}}
  String getNewIdeaPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'audio_transcription_new_idea_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for transcribing audio for chat input
  String getChatInputPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'audio_transcription_chat_input_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for chat conversations
  /// Variables: {{ideabook_name}}, {{ideas_text}}, {{user_content}}, {{past_chat_text}}
  String getChatPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'chat_conversation_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for generating suggested prompts
  /// Variables: {{ideabook_name}}, {{formatted_ideas}}, {{previous_suggestions_text}}
  String getSuggestedPromptsPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'suggested_prompts_generation_prompt',
      userTier: userTier,
    );
  }

  /// Get the prompt for note regeneration
  /// Variables: {{ideabook_name}}, {{ideas_text}}, {{note_title}}
  String getNoteRegenerationPrompt({String? userTier}) {
    return _remoteConfigService.getString(
      'note_regeneration_prompt',
      userTier: userTier,
    );
  }

  /// Replace variables in a prompt template
  String replaceVariables(String prompt, Map<String, String> variables) {
    String result = prompt;
    variables.forEach((key, value) {
      result = result.replaceAll('{{$key}}', value);
    });
    return result;
  }
}

/// Class that provides access to all LLM model configurations from Remote Config
class LlmModelConfig {
  final RemoteConfigService _remoteConfigService;
  
  LlmModelConfig(this._remoteConfigService);

  /// Get the model name for audio transcription
  String getAudioTranscriptionModel({String? userTier}) {
    return _remoteConfigService.getString(
      'audio_transcription_model_name',
      userTier: userTier,
    );
  }

  /// Get the model name for chat
  String getChatModel({String? userTier}) {
    return _remoteConfigService.getString(
      'chat_model_name',
      userTier: userTier,
    );
  }

  /// Get the model name for suggested prompts generation
  String getSuggestedPromptsModel({String? userTier}) {
    return _remoteConfigService.getString(
      'suggested_prompts_model_name',
      userTier: userTier,
    );
  }

  /// Get the model name for note regeneration
  String getNoteRegenerationModel({String? userTier}) {
    return _remoteConfigService.getString(
      'note_regeneration_model_name',
      userTier: userTier,
    );
  }

  /// Get the generation config for audio transcription
  Map<String, dynamic> getAudioTranscriptionConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'audio_transcription_generation_config',
      userTier: userTier,
    );
  }

  /// Get the generation config for chat
  Map<String, dynamic> getChatConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'chat_generation_config',
      userTier: userTier,
    );
  }

  /// Get the generation config for suggested prompts generation
  Map<String, dynamic> getSuggestedPromptsConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'suggested_prompts_generation_config',
      userTier: userTier,
    );
  }

  /// Get the generation config for note regeneration
  Map<String, dynamic> getNoteRegenerationConfig({String? userTier}) {
    return _remoteConfigService.getJson(
      'note_regeneration_generation_config',
      userTier: userTier,
    );
  }
}
